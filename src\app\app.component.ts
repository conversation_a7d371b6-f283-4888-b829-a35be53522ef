import { Component } from '@angular/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  title = 'موقع طبي تفاعلي';

  // بيانات الكاروسيل - مصفوفة تحتوي على معلومات الصور
  carouselImages = [
    {
      id: 1,
      src: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=400&fit=crop',
      title: 'الفحص الطبي',
      description: 'فحوصات طبية شاملة ودقيقة',
      detailImages: [
        'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=600&h=400&fit=crop',
        'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop',
        'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=600&h=400&fit=crop'
      ]
    },
    {
      id: 2,
      src: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=400&fit=crop',
      title: 'المختبرات الطبية',
      description: 'تحاليل مخبرية متقدمة',
      detailImages: [
        'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop',
        'https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=600&h=400&fit=crop',
        'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=600&h=400&fit=crop'
      ]
    },
    {
      id: 3,
      src: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=800&h=400&fit=crop',
      title: 'الجراحة',
      description: 'عمليات جراحية متطورة',
      detailImages: [
        'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=600&h=400&fit=crop',
        'https://images.unsplash.com/photo-1551601651-2a8555f1a136?w=600&h=400&fit=crop',
        'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=600&h=400&fit=crop'
      ]
    }
  ];

  // متغير لتتبع الصورة النشطة في الكاروسيل
  currentImageIndex = 0;

  // متغير للتحكم في إظهار/إخفاء النافذة المنبثقة
  showModal = false;

  // متغير لحفظ الصورة المحددة للعرض في النافذة المنبثقة
  selectedImage: any = null;

  // دالة للانتقال للصورة التالية في الكاروسيل
  nextImage() {
    this.currentImageIndex = (this.currentImageIndex + 1) % this.carouselImages.length;
  }

  // دالة للانتقال للصورة السابقة في الكاروسيل
  prevImage() {
    this.currentImageIndex = this.currentImageIndex === 0
      ? this.carouselImages.length - 1
      : this.currentImageIndex - 1;
  }

  // دالة لفتح النافذة المنبثقة عند الضغط على صورة
  openModal(image: any) {
    this.selectedImage = image;
    this.showModal = true;
  }

  // دالة لإغلاق النافذة المنبثقة
  closeModal() {
    this.showModal = false;
    this.selectedImage = null;
  }
}