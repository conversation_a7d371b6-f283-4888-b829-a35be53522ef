/* إعدادات عامة للموقع */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  direction: rtl;
  text-align: right;
}

/* الحاوي الرئيسي */
.main-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* تنسيق العنوان الرئيسي */
.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* قسم الشكل التفاعلي المتحرك */
.animated-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 50px 0;
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.heartbeat-container {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

/* أيقونة القلب المتحركة */
.heartbeat-icon {
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* موجة النبض المتحركة */
.pulse-wave {
  animation: pulse 2s linear infinite;
}

.pulse-line {
  stroke-dasharray: 200;
  stroke-dashoffset: 200;
  animation: draw 2s linear infinite;
}

@keyframes draw {
  to {
    stroke-dashoffset: 0;
  }
}

.animated-text {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* قسم الكاروسيل */
.carousel-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  margin: 40px auto;
  max-width: 1000px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 30px;
  font-weight: bold;
}

/* حاوي الكاروسيل */
.carousel-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.carousel-images {
  position: relative;
  width: 600px;
  height: 400px;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.3);
}

.image-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  cursor: pointer;
}

.image-wrapper.active {
  opacity: 1;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-wrapper:hover .carousel-image {
  transform: scale(1.05);
}

/* طبقة النص فوق الصورة */
.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  color: white;
  padding: 30px 20px 20px;
  text-align: center;
}

.image-overlay h3 {
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.image-overlay p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* أزرار التنقل في الكاروسيل */
.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.carousel-btn:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
}

.prev-btn {
  left: -25px;
}

.next-btn {
  right: -25px;
}

/* مؤشرات الكاروسيل */
.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #bdc3c7;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #e74c3c;
  transform: scale(1.2);
}

.indicator:hover {
  background: #e74c3c;
}

/* تنسيق النافذة المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 800px;
  max-height: 90vh;
  width: 90%;
  overflow-y: auto;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* رأس النافذة المنبثقة */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #ecf0f1;
  background: #f8f9fa;
  border-radius: 20px 20px 0 0;
}

.modal-header h3 {
  color: #2c3e50;
  font-size: 1.8rem;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #7f8c8d;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #e74c3c;
  color: white;
}

/* محتوى النافذة المنبثقة */
.modal-body {
  padding: 30px;
}

.modal-description {
  font-size: 1.2rem;
  color: #34495e;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.6;
}

/* شبكة الصور التفصيلية */
.detail-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.detail-image-wrapper {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.detail-image-wrapper:hover {
  transform: translateY(-5px);
}

.detail-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.detail-image-wrapper:hover .detail-image {
  transform: scale(1.05);
}

/* تنسيق متجاوب للشاشات الصغيرة */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }

  .carousel-images {
    width: 100%;
    height: 250px;
  }

  .carousel-btn {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }

  .prev-btn {
    left: -20px;
  }

  .next-btn {
    right: -20px;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .detail-images-grid {
    grid-template-columns: 1fr;
  }

  .heartbeat-container {
    flex-direction: column;
    gap: 10px;
  }
}