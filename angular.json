{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"my-firstapp": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main.ts", "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.css"], "server": "src/main.server.ts", "outputMode": "server", "ssr": {"entry": "src/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "my-firstapp:build:production"}, "development": {"buildTarget": "my-firstapp:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css"]}}}}}, "cli": {"analytics": "c544b362-fc41-42d3-82eb-8fe06e37fc79"}}