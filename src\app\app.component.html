<!-- الحاوي الرئيسي للموقع -->
<div class="main-container">

  <!-- العنوان الرئيسي -->
  <header class="header">
    <h1 class="title">{{ title }}</h1>
    <p class="subtitle">رعاية صحية متميزة وخدمات طبية شاملة</p>
  </header>

  <!-- الشكل التفاعلي المتحرك (نبضات القلب) -->
  <div class="animated-section">
    <div class="heartbeat-container">
      <div class="heartbeat-icon">
        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
                fill="#e74c3c" stroke="#e74c3c" stroke-width="2"/>
        </svg>
      </div>
      <div class="pulse-wave">
        <svg width="200" height="60" viewBox="0 0 200 60">
          <path d="M0,30 L40,30 L45,10 L50,50 L55,20 L60,40 L65,30 L200,30"
                stroke="#e74c3c" stroke-width="3" fill="none" class="pulse-line"/>
        </svg>
      </div>
    </div>
    <p class="animated-text">نحن نهتم بصحتك</p>
  </div>

  <!-- قسم الكاروسيل -->
  <div class="carousel-section">
    <h2 class="section-title">خدماتنا الطبية</h2>

    <div class="carousel-container">
      <!-- أزرار التنقل -->
      <button class="carousel-btn prev-btn" (click)="prevImage()">
        <span>‹</span>
      </button>

      <!-- الصور -->
      <div class="carousel-images">
        <div class="image-wrapper"
             *ngFor="let image of carouselImages; let i = index"
             [class.active]="i === currentImageIndex"
             (click)="openModal(image)">
          <img [src]="image.src" [alt]="image.title" class="carousel-image">
          <div class="image-overlay">
            <h3>{{ image.title }}</h3>
            <p>{{ image.description }}</p>
          </div>
        </div>
      </div>

      <button class="carousel-btn next-btn" (click)="nextImage()">
        <span>›</span>
      </button>
    </div>

    <!-- مؤشرات الصور -->
    <div class="carousel-indicators">
      <span *ngFor="let image of carouselImages; let i = index"
            [class.active]="i === currentImageIndex"
            (click)="currentImageIndex = i"
            class="indicator"></span>
    </div>
  </div>

</div>

<!-- النافذة المنبثقة (Modal) -->
<div class="modal-overlay" *ngIf="showModal" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>{{ selectedImage?.title }}</h3>
      <button class="close-btn" (click)="closeModal()">×</button>
    </div>

    <div class="modal-body">
      <p class="modal-description">{{ selectedImage?.description }}</p>

      <div class="detail-images-grid">
        <div *ngFor="let detailImg of selectedImage?.detailImages" class="detail-image-wrapper">
          <img [src]="detailImg" [alt]="selectedImage?.title" class="detail-image">
        </div>
      </div>
    </div>
  </div>
</div>